package login

import (
	"context"

	g "casrv/server/common"
	mgo "casrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type Mongodb struct {
	table string
}

func (this *Mongodb) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 玩家数据库
var db = &Mongodb{g.DB_COLLECTION_NAME_USER}

// 根据不同登录方式的openid查询账号
func (this *Mongodb) FindByLoginTypeOpenid(openidField string, openid string) (data g.UserTableData, err string) {
	if openidField == "" {
		err = "FindByLoginTypeOpenid loginType err"
	} else if e := this.getCollection().FindOne(context.TODO(), bson.M{openidField: openid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 根据uid查询账号
func (this *Mongodb) FindByUid(uid string) (data g.UserTableData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 插入单个
func (this *Mongodb) InsertOne(data g.UserTableData) (err string) {
	if _, e := this.getCollection().InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
		log.Error("InsertOne", err)
	}
	return
}

// 是否有相同昵称
func (this *Mongodb) HasNickname(nickname string) bool {
	if _, e := this.getCollection().FindOne(context.TODO(), bson.M{"nickname": nickname}).DecodeBytes(); e != nil {
		return false
	}
	return true
}

// 是否有相同的uid
func (this *Mongodb) HasUid(uid string) bool {
	if _, e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).DecodeBytes(); e != nil {
		return false
	}
	return true
}

// 更新一条
func (this *Mongodb) UpdateOne(uid string, key string, value interface{}) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{key: value}}); e != nil {
		err = e.Error()
		log.Error("user UpdateOne", err)
	}
	return
}
