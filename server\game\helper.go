package game

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
	"casrv/utils/array"
)

// 拷贝属性
func CloneAttrs(attrs [][]int32) [][]int32 {
	ret := [][]int32{}
	for i, l := 0, len(attrs); i < l; i++ {
		ret = append(ret, array.Clone(attrs[i]))
	}
	return ret
}

// 拷贝属性到pb
func CloneAttrsToPb(attrs [][]int32) []*pb.Int32ArrayInfo {
	arr := []*pb.Int32ArrayInfo{}
	for _, attr := range attrs {
		attrInfo := &pb.Int32ArrayInfo{}
		for _, val := range attr {
			attrInfo.Arr = append(attrInfo.Arr, val)
		}
		arr = append(arr, attrInfo)
	}
	return arr
}

// 随机3个武将
func RandomHeros(conf []string, count int32) []*Hero {
	return array.Map(conf, func(m string, i int) *Hero {
		arr := ut.StringToInt32s(m, ",")
		return NewHero(arr[0], int8(arr[1]), arr[2], AREA_TYPE_ENCOUNTER, int8(i))
	})
}
