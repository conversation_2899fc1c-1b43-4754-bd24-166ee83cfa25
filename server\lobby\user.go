package lobby

import (
	g "casrv/server/common"
	"casrv/server/common/pb"
	ut "casrv/utils"
	"sync/atomic"
	"time"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
)

type User struct {
	Session gate.Session

	UID       string
	LoginType string
	Platform  string // 平台
	Nickname  string
	Language  string // 语言
	Ip        string // ip地址
	GuestId   string // 游客id
	SessionId string // 每次登录唯一id

	UnlockRoleIds []int32 //解锁的角色ids列表

	notifyQueue chan *pb.OnUpdatePlayerInfoNotify // 通知队列

	CreateTime        int64 // 创建时间
	LastLoginTime     int64 // 最后登陆时间
	LastOfflineTime   int64 // 最后离线时间
	SumOnlineTime     int64 // 累计在线时长
	NextToDayTime     int64 // 明日开始时间
	BanAccountEndTime int64 // 封禁账号结束时间

	dbLastUpdateTime int64 // db上一次更新时间

	Version           int32 // 用户版本用于兼容
	BanAccountType    int32 // 封禁账号类型
	LoginDayCount     int32 // 登录天数
	ContinueLoginDays int32 // 连续登录天数

	RoleId int32 //当前使用的角色id

	state        int32 // 状态 0.离线 1.在线
	dbUpdateFlag int32 // 更新标记 0.无需更新 1.需要更新 2.准备更新
}

// 创建用户
func CreateUser(data g.UserTableData) *User {
	user := &User{
		Version:           data.Version,
		UID:               data.Uid,
		LoginType:         data.LoginType,
		Platform:          data.Platform,
		Nickname:          data.Nickname,
		CreateTime:        data.CreateTime,
		LastLoginTime:     data.LastLoginTime,
		LastOfflineTime:   data.LastOfflineTime,
		NextToDayTime:     data.NextToDayTime,
		LoginDayCount:     data.LoginDayCount,
		ContinueLoginDays: data.ContinueLoginDays,
		SumOnlineTime:     data.SumOnlineTime,
		Language:          data.Language,
		BanAccountType:    data.BanAccountType,
		BanAccountEndTime: data.BanAccountEndTime,
		GuestId:           data.GuestId,

		dbLastUpdateTime: int64(time.Now().UnixMilli()),
	}
	return user
}

func (this *User) ToDB() bson.M {
	return bson.M{
		"version":              this.Version,
		"platform":             this.Platform,
		"login_type":           this.LoginType,
		"nickname":             this.Nickname,
		"create_time":          this.CreateTime,
		"last_login_time":      this.LastLoginTime,
		"last_offline_time":    this.LastOfflineTime,
		"next_today_time":      this.NextToDayTime,
		"login_day_count":      this.LoginDayCount,
		"continue_login_days":  this.ContinueLoginDays,
		"role_id":              this.RoleId,
		"sum_online_time":      this.SumOnlineTime,
		"ip":                   this.Ip,
		"language":             this.Language,
		"ban_account_type":     this.BanAccountType,
		"ban_account_end_time": this.BanAccountEndTime,
	}
}

// User转为pb格式
func (this *User) ToPb() *pb.UserInfo {
	userInfo := &pb.UserInfo{
		Uid:           this.UID,
		CreateTime:    this.CreateTime,
		LoginType:     this.LoginType,
		LoginDayCount: this.LoginDayCount,
		SumOnlineTime: this.SumOnlineTime,
		Nickname:      this.Nickname,
		SessionId:     this.SessionId,
		RoleId:        this.RoleId,
	}
	return userInfo
}

func (this *User) Init(lobby *Lobby) {
	now := time.Now().UnixMilli()
	this.SetState(1)
	this.SetDbLastUpdateTime(int64(now))
	this.RunNotifyQueueTick(lobby)
}

func (this *User) SetDbUpdateFlag(flag int32) { atomic.StoreInt32(&this.dbUpdateFlag, flag) }
func (this *User) GetDbUpdateFlag() int32     { return atomic.LoadInt32(&this.dbUpdateFlag) }

func (this *User) SetDbLastUpdateTime(dbLastUpdateTime int64) {
	atomic.StoreInt64(&this.dbLastUpdateTime, dbLastUpdateTime)
}
func (this *User) GetDbLastUpdateTime() int64 { return atomic.LoadInt64(&this.dbLastUpdateTime) }

func (this *User) SetState(flag int32) { atomic.StoreInt32(&this.state, flag) }
func (this *User) GetState() int32     { return atomic.LoadInt32(&this.state) }

// 是否在线
func (this *User) IsOnline() bool {
	return this.GetState() == 1
}

// 用户离线
func (this *User) Offline() {
	if this.Session != nil {
		this.Session.Set("lid", "")
		this.Session = nil
	}
	this.SetState(0)
	this.SetDbLastUpdateTime(ut.Now())
}

// 标记
func (this *User) FlagUpdateDB() bool {
	this.SetDbUpdateFlag(1)
	return this.IsOnline()
}

// 直接發
func (this *User) SessionSendNR(topic string, body []byte) {
	if this.IsOnline() && this.Session != nil {
		this.Session.SendNR(topic, body)
	}
}

// 踢出玩家
func (this *User) Kick(kickType int8, banType int32) {
	if this.Session == nil {
		return
	} else if kickType >= 0 {
		body, _ := pb.ProtoMarshal(&pb.GAME_ONKICK_NOTIFY{Type: int32(kickType), BanType: banType})
		this.Session.SendNR(g.LOBBY_ONKICK, body)
	}
	this.Session.UnBind()
	// this.Session.SetPush("sid", "")
	this.Session.Close()
	this.Session = nil
}

// 添加到用户信息通知队列
func (this *User) PutNotifyQueue(tp int32, data *pb.OnUpdatePlayerInfoNotify) {
	if data == nil || !this.IsOnline() || this.Session == nil || this.notifyQueue == nil {
		return
	}
	data.Type = tp
	this.notifyQueue <- data
}

// 用户通知Tick
func (this *User) RunNotifyQueueTick(lobby *Lobby) {
	if this.notifyQueue != nil {
		return
	}
	this.notifyQueue = make(chan *pb.OnUpdatePlayerInfoNotify, 1000)
	go func() {
		tiker := time.NewTicker(time.Millisecond * 100) // 100毫秒通知一次
		defer func() {
			tiker.Stop()
			close(this.notifyQueue)
			this.notifyQueue = nil
		}()
		for this.IsOnline() {
			<-tiker.C
			// 通知
			if len(this.notifyQueue) <= 0 {
				continue
			}
			list := []*pb.OnUpdatePlayerInfoNotify{}
			for len(this.notifyQueue) > 0 {
				msg := <-this.notifyQueue
				list = append(list, msg)
			}
			if body, err := pb.ProtoMarshal(&pb.LOBBY_ONUPDATEUSERINFO_NOTIFY{List: list}); err == "" {
				this.SessionSendNR(g.LOBBY_UPDATE_USER_INFO, body)
			} else {
				log.Error("RunNotifyQueueTick error!")
			}
		}
	}()
}
