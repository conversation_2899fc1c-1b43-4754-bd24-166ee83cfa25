#!/bin/bash
function read_yes_no
{
    local answer=blah
    while [[ ${answer} != "yes" && ${answer} != "no" ]]
    do
        echo "   Enter (Yes/No)"
        read answer
        answer=$(echo "${answer,,}")   #To lowercase
    done

    if [[ "${answer}" == "yes" ]]
    then
        answer="y"
    else
        answer="n"
    fi
    YES_NO_ANSWER=${answer}
}
echo "   Check"
xyz=$(netstat -tnlp |grep -w $1)
if [ "$xyz" == "" ]; then
  echo "   No program run at port:$1"
  exit 0
fi
echo "   ThisPid:$$"
echo "$xyz"
read_yes_no
if [ "${YES_NO_ANSWER}" != "y" ]; then
  exit 1
fi
echo "   Kill"
netstat -tnlp |grep -w $1 |awk '{print $7}' | awk -F"/" '{print $1}'| xargs kill
