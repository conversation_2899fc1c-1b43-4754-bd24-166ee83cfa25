package rds

import (
	ut "casrv/utils"
	"fmt"

	"github.com/huyangv/vmqant/log"
)

// 用户状态常量 (避免循环依赖，直接定义)
const (
	USER_STATE_ONLINE   = 3 // 在线
	USER_STATE_IN_CACHE = 5 // 离线但在内存中
)

const (
	RDS_SCRIPT_CMD_UNKONW               = iota
	RDS_SCRIPT_CMD_LOBBY_LOAD_UPDATE    // 更新大厅服负载
	RDS_SCRIPT_CMD_SET_USER_LID         // 设置玩家大厅服id
	RDS_SCRIPT_CMD_SET_USER_STATE       // 设置玩家状态
	RDS_SCRIPT_CMD_GET_USER_MALLOC_LOCK // 获取玩家大厅服分配锁是否生效
)

var scriptCmdMap = map[int]string{
	RDS_SCRIPT_CMD_LOBBY_LOAD_UPDATE:    LOBBY_LOAD_UPDATE,
	RDS_SCRIPT_CMD_SET_USER_LID:         SET_USER_LID_SCRIPT,
	RDS_SCRIPT_CMD_SET_USER_STATE:       SET_USER_STATE_SCRIPT,
	RDS_SCRIPT_CMD_GET_USER_MALLOC_LOCK: GET_USER_MALLOC_LOCK,
}

var scriptHashMap = ut.NewMapLock[int, string]()

// 初始化redis脚本
func InitRdsScript() {
	for cmd, script := range scriptCmdMap {
		hash, err := RdsLoadScript(script)
		if err != nil {
			log.Error("InitRdsScript cmd: %v, err: %v", cmd, err)
		} else {
			scriptHashMap.Set(cmd, hash)
		}
	}
}

// 大厅服负载更新
var LOBBY_LOAD_UPDATE = `
local lobbyLoadKey = KEYS[1]
local lid = ARGV[1]
local num = tonumber(ARGV[2])
-- 设置大厅服负载
local loadNum = redis.call('hget', lobbyLoadKey, lid)
if loadNum then
    loadNum = loadNum + num    
else
    loadNum = num
end
if loadNum < 0 then
    loadNum = 0
end
redis.call('hset', lobbyLoadKey, lid, loadNum)
return lid
`

// 设置玩家大厅服id
var SET_USER_LID_SCRIPT = `
local key = KEYS[1]
local uid = ARGV[1]
local curLid = ARGV[2]
local lid = redis.call('hget', key, uid)
if lid then
    -- 该用户已在其他大厅服
    return lid
else
    -- 没在其他大厅则设置为分配的大厅服
    lid = curLid
    -- 设置用户大厅服id
    redis.call('hset', key, uid, lid)
    return lid
end
`

// 离线设置玩家状态
var SET_USER_STATE_SCRIPT = fmt.Sprintf(`
local key = KEYS[1]
local uid = ARGV[1]
local oldState = redis.call('hget', key, uid)
if oldState and (oldState == %v or oldState == %v) then
    -- 之前状态为在线或者内存中 则可设置为离线
    redis.call('hdel', key, uid)
    return 1
else
    -- 否则不处理
    return 0
end
`, USER_STATE_ONLINE, USER_STATE_IN_CACHE)

// 获取玩家大厅服分配锁是否生效
var GET_USER_MALLOC_LOCK = fmt.Sprintf(`
local listKey = KEYS[1]
local now = ARGV[1]
local listLength = redis.call('LLEN', listKey)
local timeExpire = %v
if listLength == 0 then
    -- 长度为0 锁不生效
    return 0
end
-- 获取最新的时间戳判断是否过期
local lastTime = redis.call('LINDEX', listKey, listLength - 1)
if lastTime + timeExpire < tonumber(now) then
    -- 已过期则不生效 移除列表
    redis.call('DEL', listKey)
    return 0
end
return 1
`, USER_MALLOC_LOCK_EXPIRE)
