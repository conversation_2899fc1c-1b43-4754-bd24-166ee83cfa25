package lobby

import (
	"strings"

	g "casrv/server/common"
	rds "casrv/server/common/redis"
	ut "casrv/utils"
	"casrv/utils/array"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
)

var lobbyModule *Lobby

var Module = func() module.Module {
	lobbyModule = new(Lobby)
	return lobbyModule
}

type Lobby struct {
	basemodule.BaseModule

	LID            string
	RpcUserFuncMap map[string]interface{}
	RpcTeamFuncMap map[string]interface{}
}

func (this *Lobby) GetType() string {
	return "lobby" // 很关键,需要与配置文件中的Module配置对应
}

func (this *Lobby) Version() string {
	return "1.0.0" // 可以在监控时了解代码版本
}

func (this *Lobby) GetLid() string {
	return this.LID // 获取当前大厅服id
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Lobby) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	if serverType := ut.String(app.GetSettings().Settings["ServerType"]); serverType == g.SERVER_TYPE_LOBBY || serverType == g.SERVER_TYPE_DEVOLOPMENT {

	}
}

func (this *Lobby) OnInit(app module.App, settings *conf.ModuleSettings) {
	id := settings.ID
	this.BaseModule.OnInit(this, app, settings, server.ID(id))
	this.LID = strings.Replace(id, "lobby", "", 1) // 设置元数据 方便路由的是区分节点
	this.GetServer().Options().Metadata["lid"] = this.LID

	this.InitRpc()

	this.initHDUser()

	this.initBallenceLoad()
}

func (this *Lobby) Run(closeSig chan bool) {
	RunTick(this)
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	StopTick()
	SaveAllUser()
	log.Info("%v模块保存完成!", this.GetType())
}

func (this *Lobby) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 大厅服负载初始化
func (this *Lobby) initBallenceLoad() {
	// rst, err := ut.RpcInterfaceMap(this.InvokeMatchRpc(slg.RPC_GET_LOBBY_MACH_INFO, slg.SERVER_IP))
	// if err == "" && rst != nil && len(rst) > 0 {
	// 	if ut.Bool(rst["abandon"]) {
	// 		// 已在后台停用该大厅服 则不添加负载到redis
	// 		return
	// 	}
	// }
	rds.RdsHSet(rds.RDS_LOBBY_LOAD_MAP_KEY, this.LID, 0)
}

// 发送Rpc到游戏服
func (this *Lobby) InvokeGameRpc(_func string, params ...any) (result any, err string) {
	paramsBytesArr := array.Map(params, func(m any, _ int) any { return ut.Bytes(m) })
	return this.Invoke(g.SERVER_TYPE_GAME, _func, paramsBytesArr...)
}

// 生成大厅全服唯一Uid 避免多个大厅服同时生成重复uid
func GenUid() string {
	return ut.ID() + lobbyModule.GetLid()
}
