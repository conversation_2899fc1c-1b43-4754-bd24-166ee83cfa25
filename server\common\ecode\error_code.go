package ecode

import "strconv"

type ECode int

func (this ECode) String() string {
	return "ecode." + strconv.Itoa(int(this))
}

const (
	UNKNOWN           ECode = 500000 + iota
	NOT_ACCOUNT_TOKEN       // 没有token 500001
	TOKEN_INVALID           // token无效
	NOT_BIND_UID            // 还未绑定uid
	ROOM_CLOSE              // 服务器已关闭
	TOKEN_ERROR             // token错误
	ROOM_NOT_EXIST          // 游戏服不存在
	PLAYER_NOT_EXIST        // 玩家不存在 500007
	DB_ERROR                // 数据库错误
	CUR_LOBBY_FULL          // 当前大厅服已满 500009
	LOBBY_QUEUE_UP          // 大厅服需要排队 500010
	VERSION_TOOLOW          // 版本过低 500011
	BAN_ACCOUNT             // 账号已封停 500012
	SEAT_FULL               // 没有可放入的位置 500013
	GOLD_NOT_ENOUGH         // 金币不足 500014
	HERO_NOT_EXIST          // 英雄不存在 500015
)
