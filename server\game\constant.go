package game

const (
	INIT_HP            = 20 //初始血量
	INIT_GOLD          = 10 //初始的金币
	INIT_EARNINGS      = 1  //初始的收益
	BEGIN_GAME_SHOP_ID = 1  //开始游戏的商店id

	BAG_MAX_COUNT              int32 = 6 //背包最大位置数量
	BATTLE_AREA_SEAT_MAX_COUNT int32 = 9 //一共最大的位置数量
	TODAY_ADD_SEAT_COUNT       int32 = 3 //每天增加的位置数量
	TODAY_ROUND_COUNT          int32 = 6 //每天有多少回合

	HERO_MAX_LV = 3 //英雄最大等级
)

// 各个区域最大位置
var AREA_SEAT_MAX_COUNT_MAP = map[int8]int8{
	AREA_TYPE_BATTLE:  9, //战斗区域
	AREA_TYPE_PREPARE: 6, //备战区域
}

// 单独处理的物品id
const (
	SPECIAL_ITEM_RAND_ANIMAL   = 200000 //随机青铜变异动物
	SPECIAL_ITEM_GOLD_EARNINGS = 200001 //+金币和收益
	SPECIAL_ITEM_RAND_REMAINS  = 200002 //随机青铜遗物
)

// 额外添加父节点个数权重
var ADD_FATHER_NODE_COUNT_WEIGHT = [][]int32{
	{0, 0, 0, 1},
	{0, 0, 0, 0, 1, 1, 2},
}

// 地图节点分配权重
var MAP_NODE_ALLOT_WEIGHT = []int32{
	MAP_NODE_TYPE_TAVERN, //酒馆
	MAP_NODE_TYPE_TAVERN, //酒馆
	MAP_NODE_TYPE_TAVERN, //酒馆
	MAP_NODE_TYPE_TAVERN, //酒馆
	MAP_NODE_TYPE_TAVERN, //酒馆
	// MAP_NODE_TYPE_HIERON, //神庙
	// MAP_NODE_TYPE_RAND,   //随机事件
}
