package lobby

import (
	rds "casrv/server/common/redis"
	ut "casrv/utils"
	"time"

	"github.com/huyangv/vmqant/log"
)

const CHECK_USERDB_INTERVAL int64 = ut.TIME_MINUTE * 2 //检测间隔

var (
	isRunning        = false
	updateUserDBChan = make(chan *User, 100)
)

func RunTick(app *Lobby) {
	isRunning = true
	RunCheckUserDBTick()
	RunSaveUserDBTick()
	LobbyRdsLoadCheck()
}

func StopTick() {
	isRunning = false
}

// 定时检测玩家信息
func RunCheckUserDBTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		defer tiker.Stop()
		for isRunning {
			<-tiker.C
			if !isRunning {
				break
			}
			now := time.Now().UnixMilli() // 1分钟检测3000个玩家
			users.ForEach(func(m *User, k string) bool {
				dbLastUpdateTime := m.GetDbLastUpdateTime()
				if dbLastUpdateTime <= 0 {
					m.SetDbLastUpdateTime(now)
					return true
				} else if now-dbLastUpdateTime < CHECK_USERDB_INTERVAL {
					return true
				} else if m.GetDbUpdateFlag() == 2 {
					return true
				}
				m.SetDbLastUpdateTime(now)
				if m.IsOnline() && m.GetDbUpdateFlag() == 0 {
					return true
				} else if count := len(updateUserDBChan); count < 50 {
					m.SetDbUpdateFlag(2)
					updateUserDBChan <- m
					if count >= 49 {
						return false
					}
				}
				return true
			})
		}
	}()
}

// 执行保存
func RunSaveUserDBTick() {
	go func() {
		for isRunning {
			user, ok := <-updateUserDBChan
			if !ok {
				continue
			}
			beginTime, dbLastUpdateTime := time.Now().UnixMilli(), user.GetDbLastUpdateTime()
			// 保存玩家数据到db
			del, now, err := SaveUserDb(user)
			if err != "" {
				log.Info("SaveUserDB error! uid: %v, del: %v, error: %v", user.UID, del, err)
			} else {
				log.Info("SaveUserDB uid: %v, del: %v, lut: %vms, db: %vms", user.UID, del, now-dbLastUpdateTime, now-beginTime)
			}
		}
	}()
}

// 大厅服redis负载均衡人数兼容检测
func LobbyRdsLoadCheck() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		defer tiker.Stop()
		for isRunning {
			<-tiker.C
			userNum := len(users.Map)
			rds.RdsHSet(rds.RDS_LOBBY_LOAD_MAP_KEY, lobbyModule.LID, userNum)
		}
	}()
}
