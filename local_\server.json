{"Settings": {"ServerType": "development", "MongodbURL": "mongodb://127.0.0.1:27017/casrv?w=majority", "MongodbDB": "casrv", "GameMongodbURL": "mongodb://127.0.0.1:27017/casrv?w=majority", "GameMongodbDB": "casrv", "Redis": "redis://:password@127.0.0.1:6379"}, "Module": {"gate": [{"Id": "gate001", "ProcessID": "development", "Settings": {"WSAddr": ":8653", "TCPAddr": ":8563", "TLS": false}}], "login": [{"Id": "login001", "ProcessID": "development"}], "lobby": [{"Id": "lobby1", "ProcessID": "development"}], "game": [{"Id": "game1", "ProcessID": "development", "Settings": {"Sid": 1}}]}, "Mqtt": {"WirteLoopChanNum": 10, "ReadPackLoop": 1, "ReadTimeout": 600, "WriteTimeout": 300}, "Rpc": {"MaxCoroutine": 10000, "RpcExpired": 1, "LogSuccess": false}, "Log": {"file": {"daily": true, "level": 7}}}