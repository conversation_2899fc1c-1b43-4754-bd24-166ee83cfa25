package game

import (
	"casrv/server/common/ecode"
	"casrv/server/common/pb"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Game)
}

type Game struct {
	basemodule.BaseModule
}

func (this *Game) GetType() string {
	return "game"
}

func (this *Game) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Game) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Game) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	// id := settings.ID
	// this.BaseModule.OnInit(this, app, settings, server.ID(id))
	// this.GetServer().Options().Metadata["sid"] = strings.Replace(id, "game", "", 1) //设置元数据 方便路由的时候区分节点
	this.InitRpc()

	this.GetServer().RegisterGO("HD_Entry", this.entry)                 //进入游戏
	this.GetServer().RegisterGO("HD_SelectMapNode", this.selectMapNode) //选择地图节点
	this.GetServer().RegisterGO("HD_BuyHero", this.buyHero)             //购买英雄
}

func (this *Game) Run(closeSig chan bool) {
	// r.RunAllRoom(&this.BaseModule)
	// GetRoom(1).Run()
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	// r.SaveAllRoom()
}

func (this *Game) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 进入游戏
func (this *Game) entry(session gate.Session, msg *pb.GAME_HD_ENTRY_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	// 游戏数据
	gameData := GetGameData(uid)
	if gameData == nil {
		gameData = CreateGame(uid, msg.GetNickname(), msg.GetRoleId())
		log.Info("create game uid: %v, day: %v", uid, gameData.Player.Day)
	} else {
		log.Info("entry game uid: %v, day: %v", uid, gameData.Player.Day)
	}
	// 地图数据
	mapData := GetMapData(uid)
	if mapData == nil {
		mapData = CreateMapData(uid, gameData.Player.Day)
	}
	return pb.ProtoMarshal(&pb.GAME_HD_ENTRY_S2C{MapData: mapData.ToPb(), GameData: gameData.ToPb()})
}

// 选择地图节点
func (this *Game) selectMapNode(session gate.Session, msg *pb.GAME_HD_SELECTMAPNODE_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	gameData := GetGameData(uid)
	if gameData == nil {
		log.Error("selectMapNode error, not gameData! uid: %v", uid)
		return nil, ecode.UNKNOWN.String()
	}
	mapData := GetMapData(uid)
	if mapData == nil {
		log.Error("selectMapNode error, not mapData! uid: %v", uid)
		return nil, ecode.UNKNOWN.String()
	}
	player := gameData.Player
	if player.Hour < 0 || player.Hour >= int32(len(mapData.Maps)) {
		log.Error("selectMapNode error, hour index? uid: %v, hour: %v, mapCount: %v", uid, player.Hour, len(mapData.Maps))
		return nil, ecode.UNKNOWN.String()
	}
	layer := mapData.Maps[player.Hour]
	index := msg.GetIndex()
	if index < 0 || index >= int32(len(layer)) {
		log.Error("selectMapNode error, layer index? uid: %v, index: %v, itemCount: %v", uid, index, len(layer))
		return nil, ecode.UNKNOWN.String()
	}
	node := layer[index]
	gameData.ResetEncounter(node.NodeID)
	// 添加路径
	pathId := player.Hour*10 + index
	gameData.MapPaths = append(gameData.MapPaths, pathId)
	// 小时增加
	// player.Hour += 1
	// 最后保存游戏数据
	SaveGameData(uid, gameData)
	return pb.ProtoMarshal(&pb.GAME_HD_SELECTMAPNODE_S2C{GameData: gameData.ToPb()})
}

// 购买英雄
func (this *Game) buyHero(session gate.Session, msg *pb.GAME_HD_BUYHERO_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	gameData := GetGameData(uid)
	if gameData == nil {
		log.Error("buyHero error, not gameData! uid: %v", uid)
		return nil, ecode.UNKNOWN.String()
	}
	encounter, player := gameData.Encounter, gameData.Player
	if encounter == nil || encounter.Tavern == nil {
		log.Error("buyHero error, not encounter! uid: %v", uid)
		return nil, ecode.UNKNOWN.String()
	}
	heroUid := msg.GetHeroUid()
	hero := encounter.Tavern.GetHero(heroUid)
	if hero == nil {
		log.Error("buyHero error, not hero! uid: %v, heroUid: %v", uid, heroUid)
		return nil, ecode.HERO_NOT_EXIST.String()
	} else if player.Gold < hero.Cost { // 检测费用
		return nil, ecode.GOLD_NOT_ENOUGH.String()
	}
	areaType, useIndex := msg.GetAreaType(), msg.GetUseIndex()
	// 先检测使用的位置是否正确
	if areaType > 0 && player.AddSoldierToArea(int8(areaType), int8(useIndex), hero) {
		// 添加成功
	} else if !player.AddSoldierToIdleArea(hero) {
		return nil, ecode.SEAT_FULL.String()
	}
	// 扣除费用
	player.Gold -= hero.Cost
	// 清理遭遇
	gameData.CleanEncounterData()
	// 最后保存游戏数据
	SaveGameData(uid, gameData)
	return pb.ProtoMarshal(&pb.GAME_HD_BUYHERO_S2C{GameData: gameData.ToPb()})
}
