package mgo

import (
	ut "casrv/utils"
	"context"
	"time"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	Database = "casrv"
	Client   *mongo.Client
)

// 初始化
func Init(url, dbname, serverType string) {
	Database = dbname
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  //只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) //指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(20)                  //使用最大的连接数
	var err error
	if Client, err = mongo.Connect(ctx, opt); err == nil {
		log.Info("mongodb init success! url: %v[%v]", url, Database)
	} else if err == mongo.ErrNoDocuments {
		panic("mongodb init error! ErrNoDocuments")
	} else {
		panic(err.Error())
	}
}

func GetCollectionClone(table string) *mongo.Collection {
	collection, _ := Client.Database(Database).Collection(table).Clone()
	return collection
}

func CreateCollection(table string) error {
	err := Client.Database(Database).CreateCollection(context.TODO(), table)
	if err != nil {
		log.Error("CreateCollection error! table="+table, err.Error())
	}
	return err
}

func GetCollection(table string) *mongo.Collection {
	return Client.Database(Database).Collection(table)
}

// 创建一个
func CreateClient(url string) *mongo.Client {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  // 只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) // 指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(20)                  // 使用最大的连接数
	var err error
	var cli *mongo.Client
	if cli, err = mongo.Connect(ctx, opt); err == nil {
		return cli
	} else {
		log.Error(err.Error())
	}
	return nil
}

// 添加索引（复合）
func AddIndex(collectionName string, indexFields map[string]int) {
	col := GetCollection(collectionName)
	indexView := col.Indexes()
	// 获取索引列表
	cursor, err := indexView.List(context.Background())
	if err != nil {
		log.Error("addIndexs List collectionName: %v, err: %v", collectionName, err)
		return
	}
	defer cursor.Close(context.Background())

	// 构建符合索引
	indexName := ""
	keys := bson.D{}
	for field, sort := range indexFields {
		if indexName != "" {
			indexName += "_"
		}
		keys = append(keys, bson.E{Key: field, Value: sort})
		indexName += field + "_" + ut.Itoa(sort)
	}

	// 遍历索引列表
	for cursor.Next(context.Background()) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			log.Error("addIndexs Decode collectionName: %v, err: %v", collectionName, err)
			continue
		}
		if index != nil && ut.String(index["name"]) == indexName {
			// 添加过的索引就不再添加了
			return
		}
	}
	_, err = indexView.CreateOne(context.Background(), mongo.IndexModel{
		Keys: keys,
	})
	if err != nil {
		log.Error("addIndexs collectionName: %v, indexFields: %v, err: %v", collectionName, indexFields, err)
	} else {
		log.Info("addIndexs collectionName: %v, indexFields: %v", collectionName, indexFields)
	}
}

// 添加唯一索引
func AddUniqueIndex(colName, field string, sort int) {
	col := GetCollection(colName)
	indexView := col.Indexes()
	// 获取索引列表
	cursor, err := indexView.List(context.Background())
	if err != nil {
		log.Error("AddUniqueIndex List colName: %v, err: %v", colName, err)
		return
	}
	defer cursor.Close(context.Background())
	indexName := field + "_" + ut.Itoa(sort)
	// 遍历索引列表
	for cursor.Next(context.Background()) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			log.Error("AddUniqueIndex Decode collectionName: %v, err: %v", field, err)
			continue
		}
		if index != nil && ut.String(index["name"]) == indexName {
			// 添加过的索引就不再添加了
			return
		}
	}
	_, err = indexView.CreateOne(context.Background(), mongo.IndexModel{
		Keys:    bson.M{field: sort},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		log.Error("AddUniqueIndex colName: %v, filed: %v, err: %v", colName, field, err)
	} else {
		log.Info("AddUniqueIndex colName: %v, filed: %v", colName, field)
	}
}
