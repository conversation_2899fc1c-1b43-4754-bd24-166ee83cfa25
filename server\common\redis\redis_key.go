package rds

const GNS = "ac"

// redis键
const (
	RDS_LOBBY_LOAD_MAP_KEY     = GNS + "_lobby_load_map"      // 大厅服负载map
	RDS_USER_LID_MAP_KEY       = GNS + "_user_lid_map"        // 玩家lid map
	RDS_USER_LID_LOCK_KEY      = GNS + "_user_lid_lock_"      // 分配玩家lid锁 key+uid 值为加锁的时间
	RDS_GAME_DATA_KEY          = GNS + "_game_data_"          // 游戏数据
	RDS_GAME_COPY_DATA_DAY_KEY = GNS + "_game_copy_data_day_" // 玩家的游戏数据 根据天拷贝 备份
)

// redis用户数据field
const (
	RDS_USER_FIELD_LID   = "lid"
	RDS_USER_FIELD_TOKEN = "token"
)
