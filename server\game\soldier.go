package game

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
)

// 一个英雄
type Hero struct {
	UID      string    `json:"uid"`
	Attrs    [][]int32 `json:"attrs"` //属性 {type, id, value} type: 0.基础属性 1.效果
	Id       int32     `json:"id"`
	Cost     int32     `json:"cost"` //费用
	Lv       int8      `json:"lv"`
	AreaType int8      `json:"areaType"` //所在区域
	Index    int8      `json:"index"`    //位置
}

func (this *Hero) ToPb() *pb.Hero {
	return &pb.Hero{
		Uid:      this.UID,
		Id:       this.Id,
		Lv:       int32(this.Lv),
		Attrs:    CloneAttrsToPb(this.Attrs),
		Cost:     this.Cost,
		AreaType: int32(this.AreaType),
		Index:    int32(this.Index),
	}
}

func NewHero(id int32, lv int8, cost int32, areaType, index int8) *Hero {
	return &Hero{UID: ut.ID(), Id: id, Lv: lv, Cost: cost, AreaType: areaType, Index: index}
}

// 是否满级
func (this *Hero) IsMaxLv() bool {
	return this.Lv >= HERO_MAX_LV
}

// 升级
func (this *Hero) UpLv(hero *Hero) {
	this.Lv += 1
	// 提取生命和攻击
	baseAttrMap := map[int32]int32{}
	for _, attr := range hero.Attrs {
		if attr[0] == 0 {
			baseAttrMap[attr[1]] = attr[2]
		}
	}
	// 将基础属性添加上去 生命和攻击
	for _, attr := range this.Attrs {
		if attr[0] == 0 {
			attr[2] += baseAttrMap[attr[1]]
		}
	}
}
