package ut

import (
	"math"
)

type Vec2 struct {
	X int `json:"x" bson:"x"`
	Y int `json:"y" bson:"y"`
}

func NewVec2(x int, y int) *Vec2 {
	return &Vec2{X: x, Y: y}
}

func NewVec2ByObj(val any) *Vec2 {
	switch reply := val.(type) {
	case map[string]any:
		return NewVec2(Int(reply["x"]), Int(reply["y"]))
	case *Vec2:
		return reply.Clone()
	case nil:
		return NewVec2(0, 0)
	}
	return NewVec2(0, 0)
}

func NewVec2ByString(str, separator string) *Vec2 {
	arr := StringToInts(str, separator)
	return NewVec2(arr[0], arr[1])
}

func (this *Vec2) ID() string {
	return this.Join("_")
}

func (this *Vec2) Equals(vec *Vec2) bool {
	return vec.X == this.X && vec.Y == this.Y
}

func (this *Vec2) Equal2(x, y int) bool {
	return x == this.X && y == this.Y
}

func (this *Vec2) Init(x int, y int) {
	this.X = x
	this.Y = y
}

func (this *Vec2) Join(separator string) string {
	return Itoa(this.X) + separator + Itoa(this.Y)
}

func (this *Vec2) Set(vec *Vec2) {
	this.X = vec.X
	this.Y = vec.Y
}

func (this *Vec2) Clone() *Vec2 {
	return NewVec2(this.X, this.Y)
}

func (this *Vec2) AddSelf(vec *Vec2) *Vec2 {
	this.X += vec.X
	this.Y += vec.Y
	return this
}

func (this *Vec2) Add(vec *Vec2) *Vec2 {
	return NewVec2(this.X+vec.X, this.Y+vec.Y)
}

func (this *Vec2) Sub(vec *Vec2) *Vec2 {
	return NewVec2(this.X-vec.X, this.Y-vec.Y)
}

// 向量除法
func (this *Vec2) DivSelf(v int) *Vec2 {
	this.X /= v
	this.Y /= v
	return this
}

func (this *Vec2) Div(v int) *Vec2 {
	return NewVec2(this.X/v, this.Y/v)
}

// 返回长度
func (this *Vec2) Len() int {
	return Abs(this.X) + Abs(this.Y)
}

func (this *Vec2) Mul(mul int) *Vec2 {
	return NewVec2(this.X*mul, this.Y*mul)
}

func (this *Vec2) Mag() float64 {
	a := this.X*this.X + this.Y*this.Y
	return math.Sqrt(float64(a))
}

func (this *Vec2) MagSqr() int {
	return this.X*this.X + this.Y*this.Y
}

func (this *Vec2) ToIndex(size *Vec2) int {
	if this.X < 0 || this.X >= size.X || this.Y < 0 || this.Y >= size.Y {
		return -1
	}
	return this.Y*size.X + this.X
}
