package game

import (
	"casrv/server/common/pb"
	"casrv/utils/array"
)

// 酒馆
type TavernInfo struct {
	Id    int32   `json:"id"`    //id
	<PERSON><PERSON> []*Hero `json:"heros"` //英雄列表
}

func (this *TavernInfo) ToPb() *pb.TavernInfo {
	return &pb.TavernInfo{Id: this.Id, Heros: array.Map(this.Heros, func(m *Hero, _ int) *pb.Hero { return m.ToPb() })}
}

func (this *TavernInfo) GetHero(uid string) *Hero {
	if hero := array.Find(this.Heros, func(m *Hero) bool { return m.UID == uid }); hero != nil {
		return hero
	}
	return nil
}

// 遭遇信息
type EncounterInfo struct {
	Player *PlayerInfo `json:"player"` //玩家信息
	// Data_2 *TavernInfo `json:"data_2"` //战斗
	Tavern *TavernInfo `json:"tavern"` //酒馆信息

	Type       int32 `json:"type"`       //类型
	UpdateCost int32 `json:"updateCost"` //刷新费用 0.表示不可刷新
}

func (this *EncounterInfo) ToPb() *pb.EncounterInfo {
	info := &pb.EncounterInfo{Type: this.Type, UpdateCost: this.UpdateCost}
	switch this.Type {
	case MAP_NODE_TYPE_PLAYER:
		if this.Player != nil {
			info.Data_1 = this.Player.ToPb()
		}
	case MAP_NODE_TYPE_BATTLE:
	case MAP_NODE_TYPE_TAVERN:
		if this.Tavern != nil {
			info.Data_3 = this.Tavern.ToPb()
		}
	}
	return info
}

// 清理数据
func (this *EncounterInfo) CleanData() {
	this.Player = nil
	this.Tavern = nil
}

// 重置
func (this *EncounterInfo) Reset(tp int32) {
	this.CleanData()
	this.Type = tp
	switch tp {
	case MAP_NODE_TYPE_PLAYER:
		// this.Encounter = CreatePlayer()
	case MAP_NODE_TYPE_BATTLE:
		this.Tavern = CreateTavernEncounter()
	case MAP_NODE_TYPE_TAVERN:
		this.Tavern = CreateTavernEncounter()
	}
}

// // 创建战斗遭遇
// func CreateBattleEncounter() *EncounterInfo {
// 	// return &EncounterInfo{
// 	// 	Type: MAP_NODE_TYPE_BATTLE,
// 	// }
// 	return CreateTavernEncounter()
// }

// 创建酒馆遭遇
func CreateTavernEncounter() *TavernInfo {
	tavern := new(TavernInfo)
	tavern.Heros = RandomHeros(SHOP_RANDOM_ITEMS[SPECIAL_ITEM_RAND_ANIMAL], 3)
	return tavern
}
