package ut

import "github.com/sasha-s/go-deadlock"

type MapLock[K comparable, V any] struct {
	deadlock.RWMutex
	Map map[K]V
}

func NewMapLock[K comparable, V any]() *MapLock[K, V] {
	return &MapLock[K, V]{Map: map[K]V{}}
}

func (this *MapLock[K, V]) Get(key K) V {
	this.RLock()
	defer this.RUnlock()
	return this.Map[key]
}

func (this *MapLock[K, V]) Set(key K, val V) {
	this.Lock()
	this.Map[key] = val
	this.Unlock()
}

func (this *MapLock[K, V]) Del(key K) {
	this.Lock()
	delete(this.Map, key)
	this.Unlock()
}

func (this *MapLock[K, V]) Count() int {
	this.RLock()
	defer this.RUnlock()
	return len(this.Map)
}

func (this *MapLock[K, V]) Clean() {
	this.Lock()
	this.Map = map[K]V{}
	this.Unlock()
}

func (this *MapLock[K, V]) ForEach(callback func(v V, k K) bool) {
	this.RLock()
	defer this.RUnlock()
	for k, v := range this.Map {
		if !callback(v, k) {
			return
		}
	}
}
