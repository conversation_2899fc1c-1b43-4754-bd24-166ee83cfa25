package tick

import (
	"time"
)

type Tick struct {
	interval  float64
	isRunning bool
	callback  func(dt int, now int) bool
}

func (this *Tick) run() {
	if this.callback == nil || this.interval == 0 {
		return
	}
	go func() {
		tiker := time.NewTicker(time.Millisecond * time.Duration(this.interval))
		defer tiker.Stop()
		last := time.Now().UnixNano()
		for this.isRunning {
			now := <-tiker.C
			dt := int((now.UnixNano() - last) / 1e6)
			last = now.UnixNano()
			t := int(now.UnixNano() / 1e6)
			if !this.isRunning {
				break
			} else if ok := this.callback(dt, t); !ok {
				this.Stop()
				break
			}
		}
	}()
}

func (this *Tick) Stop() {
	this.isRunning = false
	this.callback = nil
}

func Run(interval float64, cb func(dt int, now int) bool) *Tick {
	tick := &Tick{interval: interval, callback: cb}
	tick.isRunning = true
	tick.run()
	return tick
}
