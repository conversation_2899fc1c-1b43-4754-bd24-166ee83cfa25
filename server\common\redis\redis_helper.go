package rds

import (
	"context"
	"errors"
	"math/rand"
	"net/url"
	"time"

	ut "casrv/utils"

	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
)

const (
	LOBBY_ASSIGN_MIN_NUM    = 100                // 大厅服负载均衡最少人数
	LOBBY_ASSIGN_MAX_NUM    = 10000              // 大厅服负载均衡大厅人数上限
	USER_MALLOC_LOCK_EXPIRE = ut.TIME_SECOND * 5 // 玩家大厅服分配锁超时时间
)

var (
	DEBUG           = true
	Client          *redis.Client
	TranslateClient *redis.Client
	RedsyncInst     *redsync.Redsync
)

// 初始化
func Init(urlStr string) {
	redisURL, err := url.Parse(urlStr)
	if err != nil {
		panic(err)
	}
	password, _ := redisURL.User.Password()
	// 创建 Redis 客户端
	Client = redis.NewClient(&redis.Options{
		Addr:     redisURL.Host,
		Password: password,
	})
	InitRdsScript()

	// 创建分布式锁
	pool := goredis.NewPool(Client)
	RedsyncInst = redsync.New(pool)
	log.Info("redis init done. url: %v", urlStr)
}

func RdsSet(key string, val any) error {
	return Client.Set(context.TODO(), key, val, 0).Err()
}

func RdsGet(key string) (string, error) {
	return Client.Get(context.TODO(), key).Result()
}

func RdsDelKeys(keys ...string) (int64, error) {
	return Client.Del(context.TODO(), keys...).Result()
}

func RdsSetEx(key string, val any, exTimeMil int) error {
	return Client.SetEx(context.TODO(), key, val, time.Millisecond*time.Duration(exTimeMil)).Err()
}

func RdsHSet(key, field string, val any) error {
	return Client.HSet(context.TODO(), key, field, val).Err()
}

func RdsHSetMap(key string, mapData map[string]any) error {
	return Client.HSet(context.TODO(), key, mapData).Err()
}

func RdsHGet(key, field string) (string, error) {
	return Client.HGet(context.TODO(), key, field).Result()
}

func RdsHGetAll(key string) (map[string]string, error) {
	return Client.HGetAll(context.TODO(), key).Result()
}

func RdsHDel(key, field string) error {
	return Client.HDel(context.TODO(), key, field).Err()
}

// 获取redis pipeline
func RdsPipeline() redis.Pipeliner {
	return Client.Pipeline()
}

// 获取列表指定范围内的元素
func RdsGetListRange(key string, start, end int) ([]string, error) {
	return Client.LRange(context.TODO(), key, int64(start), int64(end)).Result()
}

// 在列表中添加一个或多个值到列表尾部
func RdsListRPush(key string, values ...any) (int64, error) {
	return Client.RPush(context.TODO(), key, values).Result()
}

// 移除列表元素 count>0 从表尾 count<0 从表头
func RdsLRemove(key string, count int, value any) (int64, error) {
	return Client.LRem(context.TODO(), key, int64(count), value).Result()
}

// 移除列表第一个元素
func RdsLPop(key string) (string, error) {
	return Client.LPop(context.TODO(), key).Result()
}

// 执行指定hash的redis脚本
func RdsEvalHash(hash string, keys []string, args ...any) (any, error) {
	return Client.EvalSha(context.TODO(), hash, keys, args).Result()
}

// 加载脚本到redis
func RdsLoadScript(script string) (string, error) {
	return Client.ScriptLoad(context.TODO(), script).Result()
}

// 加载指定cmd的脚本到redis
func RdsLoadScriptByCmd(cmd int) (hash string, err error) {
	script, ok := scriptCmdMap[cmd]
	if !ok {
		// 脚本不存在
		log.Error("RdsEvalHashByCmd script not found cmd: %v", cmd)
		err = errors.New("script not found")
		return
	}
	return RdsLoadScript(script)
}

// 根据cmdId执行指定hash的redis脚本
func RdsEvalHashByCmd(cmd int, keys []string, args ...any) (rst any, err error) {
	hash := scriptHashMap.Get(cmd)
	if hash == "" {
		// 本地没有则加载到redis
		hash, err = RdsLoadScriptByCmd(cmd)
		if err != nil {
			log.Error("RdsEvalHashByCmd load script cmd: %v, err: %v", cmd, err)
			return
		}
		log.Info("RdsEvalHashByCmd load script cmd: %v", cmd)
		scriptHashMap.Set(cmd, hash)
	}
	return RdsEvalHash(hash, keys, args...)
}

// 批量注册订阅
func RdsSubChannels(channels ...string) *redis.PubSub {
	return Client.Subscribe(context.TODO(), channels...)
}

// 发布订阅消息
func RdsPublish(channel string, msg any) {
	Client.Publish(context.TODO(), channel, msg)
}

// 订阅添加频道
func RdsSubAddChannels(sub *redis.PubSub, channels ...string) {
	sub.Subscribe(context.TODO(), channels...)
}

// 取消订阅频道
func RdsUnSubChannels(sub *redis.PubSub, channels ...string) {
	sub.Unsubscribe(context.TODO(), channels...)
}

// 获取用户所在大厅服id
func GetUserLid(uid string) string {
	return MallocUserLid(uid, false)
}

// 获取随机大厅服id
func GetRandomLid() string {
	data, err := RdsHGetAll(RDS_LOBBY_LOAD_MAP_KEY)
	if err != nil {
		log.Error("GetRandomLid err: %v", err)
		return ""
	}
	if len(data) == 0 {
		log.Error("GetRandomLid redis lid map nil")
		return ""
	}
	list := []string{}
	for lid := range data {
		list = append(list, lid)
	}
	index := rand.Intn(len(data))
	return list[index]
}

// 分配玩家大厅服id
func MallocUserLid(uid string, isLogin bool) string {
	AddUserMallocLidLock(uid)
	lid, err := RdsHGet(RDS_USER_LID_MAP_KEY, uid)
	if err == nil && lid != "" {
		// 该用户已在其他大厅服
		log.Info("MallocUserLid uid: %v, lid: %v", uid, lid)
		return lid
	}
	if err != nil {
		log.Info("MallocUserLid uid: %v, err: %v", uid, err)
	}
	// 分配大厅服
	lid = LobbyMalloc(isLogin)
	if lid != "" {
		rst, err := RdsEvalHashByCmd(RDS_SCRIPT_CMD_SET_USER_LID, []string{RDS_USER_LID_MAP_KEY}, uid, lid)
		if err != nil {
			log.Error("MallocUserLid eval script lid: %v err: %v", lid, err)
			return ""
		} else {
			lid = ut.String(rst)
		}
	}
	return lid
}

// 大厅服负载均衡 分配到负载最小的大厅服
func LobbyMalloc(isLogin bool) string {
	rst, err := RdsHGetAll(RDS_LOBBY_LOAD_MAP_KEY)
	if err == nil && rst != nil {
		maxCfg := LOBBY_ASSIGN_MAX_NUM
		if !isLogin {
			// 非登录的其他逻辑分配大厅服 负载上限暂定为10倍
			maxCfg *= 10
		}
		minNum := maxCfg
		minLid := ""
		for id, num := range rst {
			userNum := ut.Int(num)
			if len(rst) == 1 && userNum < maxCfg {
				// 只有一个大厅服且未达到上限
				return id
			}
			if userNum < minNum {
				minNum = userNum
				minLid = id
			}
		}
		if len(rst) > 0 && !isLogin && minLid == "" {
			// 非登录的其他逻辑分配大厅服 负载上限膨胀后仍超出上限
			log.Warning("LobbyMalloc not login over limit")
		}
		// 返回负载最小的大厅服id
		return minLid
	}
	return ""
}

// 更新大厅服负载
func UpdateLobbyLoad(uid, lid string, isAdd bool) {
	if isAdd {
		// 负载增加
		RdsEvalHashByCmd(RDS_SCRIPT_CMD_LOBBY_LOAD_UPDATE, []string{RDS_LOBBY_LOAD_MAP_KEY}, lid, 1)
	} else {
		// 负载减少
		lid, err := RdsHGet(RDS_USER_LID_MAP_KEY, uid)
		if err == nil && lid != "" && lid != "0" {
			RdsEvalHashByCmd(RDS_SCRIPT_CMD_LOBBY_LOAD_UPDATE, []string{RDS_LOBBY_LOAD_MAP_KEY}, lid, -1)
		} else {
			log.Warning("UpdateLobbyLoad decrease err: %v")
		}
		log.Info("remove user uid: %v, lid: %v", uid, lid)
		// 移除大厅服id
		RdsHDel(RDS_USER_LID_MAP_KEY, uid)
	}
}

// 添加玩家大厅服分配锁
func AddUserMallocLidLock(uid string) {
	key := getUserMallocLidKey(uid)
	RdsListRPush(key, ut.Now())
}

// 移除最早的玩家大厅服分配锁
func RemUserMallocLidLock(uid string) {
	key := getUserMallocLidKey(uid)
	RdsLPop(key)
}

// 移除大厅服分配锁
func DelUserMallocLidLock(uid string) {
	key := getUserMallocLidKey(uid)
	RdsDelKeys(key)
}

// 获取玩家大厅服分配锁是否生效
func GetUserMallocLidLock(uid string) bool {
	key := getUserMallocLidKey(uid)
	rst, err := RdsEvalHashByCmd(RDS_SCRIPT_CMD_GET_USER_MALLOC_LOCK, []string{key}, ut.Now())
	if err == nil && ut.String(rst) == "1" {
		return true
	}
	return false
}

func getUserMallocLidKey(uid string) string {
	return RDS_USER_LID_LOCK_KEY + uid
}

func TsltRdsSet(key string, val any) error {
	if TranslateClient == nil {
		return errors.New("redis client not init")
	}
	return TranslateClient.Set(context.TODO(), key, val, 0).Err()
}

func TsltRdsGet(key string) (string, error) {
	if TranslateClient == nil {
		return "", errors.New("redis client not init")
	}
	return TranslateClient.Get(context.TODO(), key).Result()
}
