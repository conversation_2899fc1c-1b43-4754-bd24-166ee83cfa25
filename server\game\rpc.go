package game

import (
	g "casrv/server/common"
	"casrv/server/common/pb"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitRpc() {
	this.GetServer().RegisterGO(g.RPC_LEAVE, this.leave)
	this.GetServer().RegisterGO(g.RPC_CHECK_INGAME, this.checkInGame) //检测是否在游戏中
}

// 有玩家离开
func (this *Game) leave(session gate.Session) (result any, err string) {

	return
}

// 检测是否在游戏中
func (this *Game) checkInGame(uid string) (result []byte, err string) {
	data := GetGameData(uid)
	if data == nil || data.Player == nil {
		return nil, ""
	}
	return pb.ProtoMarshal(data.Player.ToBasePb())
}
