{"name": "compile-proto", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build-proto": "npm run build-proto:pbjs && npm run build-proto:pbts", "build-proto:pbjs": "pbjs --target static-module --wrap ./wrap-pbjs.js --out ./msg.js ./msg.proto", "build-proto:pbts": "pbts --main --out ./msg.d.ts ./msg.js && node ./wrap-pbts"}, "author": "", "license": "ISC", "devDependencies": {"fs-extra": "^9.0.0", "protobufjs": "^6.8.9"}}