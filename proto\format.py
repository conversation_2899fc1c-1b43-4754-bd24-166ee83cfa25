import os
import argparse

parser = argparse.ArgumentParser() 
parser.add_argument('dir', type=str)

def format(path):
    f_list = os.listdir(path)
    for i in f_list:
        file = path + i
        if os.path.splitext(i)[1] == '.ts':
            file_data = ""            
            with open(file, "r", encoding="utf-8") as f:
                for HH in f:                    
                    if "import * as pb_1 from \"google-protobuf\";" in HH:
                        file_data += "//@ts-ignore"
                        file_data += "\n"
                    if "#one_of_decls:" in HH:
                        HH = HH.replace("#one_of_decls:","one_of_decls:")
                    if "this.#one_of_decls" in HH:
                        HH = HH.replace("this.#one_of_decls","this.one_of_decls")
                    file_data += HH

            with open(file,"w",encoding="utf-8") as f:
                f.write(file_data)
                

if __name__ == '__main__':
    args = parser.parse_args()
    format(args.dir)