package game

import (
	"casrv/server/common/pb"
	ut "casrv/utils"
)

type Areas map[int8]*Hero

fu

// 玩家的信息
type PlayerInfo struct {
	UID          string `json:"uid"`
	Nickname     string `json:"nickname"`
	BattleAreas  Areas  `json:"battleAreas"`  //战斗区域
	PrepareAreas Areas  `json:"prepareAreas"` //备战区域
	HP           int32  `json:"hp"`           //生命
	RoleId       int32  `json:"roleId"`       //角色id
	Day          int32  `json:"day"`          //天数
	Hour         int32  `json:"hour"`         //小时
	WinCount     int32  `json:"winCount"`     //胜利次数
	Gold         int32  `json:"gold"`         //金币
	Earnings     int32  `json:"earnings"`     //收益
}

func (this *PlayerInfo) ToAreasPb(areas Areas) map[int32]*pb.Hero {
	ret := map[int32]*pb.Hero{}
	for k, v := range areas {
		ret[int32(k)] = v.ToPb()
	}
	return ret
}

func (this *PlayerInfo) ToPb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		BattleAreas:  this.ToAreasPb(this.BattleAreas),
		PrepareAreas: this.ToAreasPb(this.PrepareAreas),
		Uid:          this.UID,
		Nickname:     this.Nickname,
		RoleId:       this.RoleId,
		Day:          this.Day,
		Hour:         this.Hour,
		Hp:           this.HP,
		WinCount:     this.WinCount,
		Gold:         this.Gold,
		Earnings:     this.Earnings,
	}
}

func (this *PlayerInfo) ToBasePb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		Day:      this.Day,
		Hour:     this.Hour,
		Hp:       this.HP,
		WinCount: this.WinCount,
	}
}

func (this *PlayerInfo) GetBattleAreaIdleSiteCount(areas Areas) int8 {
	var count int8 = 0
	for _, area := range areas {
		count += ut.If[int8](area != nil, 1, 0)
	}
	return count
}

// 找出区域的空闲位置index
func (this *PlayerInfo) FindAreaIdleSiteIndex(areas Areas, tp int8) int8 {
	for i, max := int8(0), AREA_SEAT_MAX_COUNT_MAP[tp]; i < max; i++ {
		if areas[i] == nil {
			return i
		}
	}
	return -1
}

// 将武将添加到指定区域
func (this *PlayerInfo) AddSoldierToArea(areaType, useIndex int8, hero *Hero) bool {
	maxSeat := AREA_SEAT_MAX_COUNT_MAP[areaType]
	if maxSeat == 0 || useIndex < 0 || useIndex >= maxSeat {
		return false
	}
	areas := ut.If(areaType == AREA_TYPE_BATTLE, this.BattleAreas, this.PrepareAreas)
	originalSoldier := areas[useIndex]
	// 如果这个位置没有武将 那么就直接放入
	if originalSoldier == nil {
		hero.AreaType = areaType
		areas[useIndex] = hero //直接添加
		return true
	}
	// 如果这个位置已经有武将 那么必须就必须和购买的一样 并没有满级 则升级
	if originalSoldier.Id != hero.Id || originalSoldier.IsMaxLv() {
		return false
	}
	// 升级
	originalSoldier.UpLv(hero)
	return true
}

// 添加到空闲位置
func (this *PlayerInfo) AddSoldierToIdleArea(hero *Hero) bool {
	// 先看战斗区域是否有空位 再看备战区域是否有位置
	if index := this.FindAreaIdleSiteIndex(this.BattleAreas, AREA_TYPE_BATTLE); index != -1 {
		hero.AreaType = AREA_TYPE_BATTLE
		hero.Index = index
		this.BattleAreas[index] = hero //直接添加
		return true
	} else if index := this.FindAreaIdleSiteIndex(this.PrepareAreas, AREA_TYPE_PREPARE); index != -1 {
		hero.AreaType = AREA_TYPE_PREPARE
		this.PrepareAreas[index] = hero //直接添加
		return true
	}
	return false
}
