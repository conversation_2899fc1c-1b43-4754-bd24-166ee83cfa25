package game

import (
	"casrv/server/common/pb"
	rds "casrv/server/common/redis"
	ut "casrv/utils"
	"casrv/utils/array"
	"encoding/json"
	"slices"

	"github.com/huyangv/vmqant/log"
)

// 一个地图节点
type MapNode struct {
	Children []int32 `json:"children"` //子节点ID
	NodeID   int32   `json:"nodeId"`   //节点ID
}

// 地图数据
type MapData struct {
	Maps [][]*MapNode `json:"maps"` //地图数据
}

func (this *MapData) ToPb() *pb.MapData {
	return &pb.MapData{Maps: array.Map(this.Maps, func(layer []*MapNode, _ int) *pb.MapLayer {
		return &pb.MapLayer{Nodes: array.Map(layer, func(m *MapNode, _ int) *pb.MapNode {
			return &pb.MapNode{NodeId: m.NodeID, Children: array.Clone(m.Children)}
		})}
	})}
}

// 获取地图数据
func GetMapData(uid string) *MapData {
	jsonStr, err := rds.RdsHGet(rds.RDS_GAME_DATA_KEY+uid, "map")
	if err == nil && jsonStr != "" {
		mapData := &MapData{}
		// 将JSON字符串反序列化为MapData对象
		if err := json.Unmarshal([]byte(jsonStr), mapData); err != nil {
			log.Error("GetMapData unmarshal error: %v", err)
		} else {
			return mapData
		}
	}
	return nil
}

func CreateMapData(uid string, day int32) *MapData {
	mapData := &MapData{}
	// 随机地图数据
	mapData.Maps = GenerateMapData(TODAY_ROUND_COUNT, day)
	// 保存
	SaveMapData(uid, mapData)
	return mapData
}

// 保存游戏数据到redis
func SaveMapData(uid string, data *MapData) error {
	// 将MapData序列化为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		log.Error("SaveMapData marshal error: %v", err)
		return err
	}
	// 保存到redis hash中
	return rds.RdsHSet(rds.RDS_GAME_DATA_KEY+uid, "map", string(jsonBytes))
}

// 生成指定层数的地图数据
func GenerateMapData(layers, day int32) [][]*MapNode {
	maps := [][]*MapNode{}
	ut.ShuffleSlice(MAP_NODE_ALLOT_WEIGHT)
	// 生成每一层
	for layer := range layers {
		currentLayer := []*MapNode{}
		// 第一天的第一层 必定酒馆
		isFirstDayOneLayer := day == 1 && layer == 0
		// 第1层 固定3个节点
		nodeCount := int32(3)
		// 最后一层固定1个节点 其余每层2-5个节点
		isPlayer, isBattle := layer == TODAY_ROUND_COUNT-1, layer == TODAY_ROUND_COUNT/2-1
		if isPlayer || isFirstDayOneLayer {
			nodeCount = 1
		} else if layer > 0 {
			nodeCount = ut.RandomInt32(2, 5)
		}
		for range nodeCount {
			node := &MapNode{Children: []int32{}}
			if isFirstDayOneLayer {
				node.NodeID = randomMapNodeId(MAP_NODE_TYPE_TAVERN) //必定为酒馆
			} else if isPlayer {
				node.NodeID = randomMapNodeId(MAP_NODE_TYPE_PLAYER) //必定为玩家
			} else if isBattle {
				node.NodeID = randomMapNodeId(MAP_NODE_TYPE_BATTLE) //第3层必定战斗
			} else {
				node.NodeID = randomMapNodeId(MAP_NODE_ALLOT_WEIGHT[int32(ut.Random(0, len(MAP_NODE_ALLOT_WEIGHT)-1))])
			}
			currentLayer = append(currentLayer, node)
		}
		// 为上一层的节点分配子节点（避免路径交叉）
		if layer > 0 {
			assignChildrenByStrictRegions(maps[layer-1], currentLayer)
		}
		maps = append(maps, currentLayer)
	}
	return maps
}

// 随机节点id
func randomMapNodeId(tp int32) int32 {
	return tp
}

// 严格的区域分割算法，确保不会有路径交叉且每个子节点都有父节点
func assignChildrenByStrictRegions(parentLayer []*MapNode, childLayer []*MapNode) {
	parentCount := len(parentLayer)
	childCount := len(childLayer)
	if parentCount == 0 || childCount == 0 {
		return
	}
	// 第一步：确保每个子节点都有至少一个父节点
	childHasParent := make([]bool, childCount)
	// 为每个父节点分配严格的子节点区域
	for i, parent := range parentLayer {
		// 计算当前父节点的严格区域
		startIndex, endIndex := calculateStrictRegion(i, parentCount, childCount)
		// 确保区域内至少有一个子节点被连接
		if startIndex <= endIndex {
			// 随机选择区域内的一个子节点作为必连节点
			guaranteedChild := ut.RandomInt32(int32(startIndex), int32(endIndex))
			parent.Children = append(parent.Children, guaranteedChild)
			childHasParent[guaranteedChild] = true
		}
	}
	// 第二步：检查是否有子节点没有父节点，如果有则分配给最近的父节点
	for childIndex, hasParent := range childHasParent {
		if !hasParent {
			// 找到最适合的父节点（区域包含此子节点的父节点）
			bestParent := findBestParentForChild(childIndex, parentCount, childCount)
			if bestParent >= 0 && bestParent < len(parentLayer) {
				parentLayer[bestParent].Children = append(parentLayer[bestParent].Children, int32(childIndex))
				childHasParent[childIndex] = true
			}
		}
	}
	// 第三步：允许多个父节点连接到同一个子节点（在不交叉的前提下）
	for i, parent := range parentLayer {
		// 为当前父节点添加额外的子节点连接
		addAdditionalConnections(parent, i, parentLayer, childCount)
	}
}

// 计算父节点的严格区域，确保不会交叉
func calculateStrictRegion(parentIndex, parentCount, childCount int) (int, int) {
	if parentCount == 1 {
		// 如果只有一个父节点，可以连接所有子节点
		return 0, childCount - 1
	}
	// 将子节点平均分配给父节点，不允许重叠
	baseSize := childCount / parentCount
	remainder := childCount % parentCount
	// 计算当前父节点的起始位置
	startIndex := parentIndex * baseSize
	if parentIndex < remainder {
		startIndex += parentIndex
	} else {
		startIndex += remainder
	}
	// 计算当前父节点的区域大小
	regionSize := baseSize
	if parentIndex < remainder {
		regionSize++
	}
	endIndex := startIndex + regionSize - 1
	// 确保索引在有效范围内
	if startIndex >= childCount {
		startIndex = childCount - 1
	}
	if endIndex >= childCount {
		endIndex = childCount - 1
	}
	if startIndex > endIndex {
		endIndex = startIndex
	}
	return startIndex, endIndex
}

// 为孤立的子节点找到最适合的父节点
func findBestParentForChild(childIndex, parentCount, childCount int) int {
	// 找到哪个父节点的区域包含这个子节点
	for parentIndex := range parentCount {
		startIndex, endIndex := calculateStrictRegion(parentIndex, parentCount, childCount)
		if childIndex >= startIndex && childIndex <= endIndex {
			return parentIndex
		}
	}
	// 如果没有找到合适的区域（理论上不应该发生），返回最近的父节点
	bestParent := 0
	minDistance := childCount
	for parentIndex := range parentCount {
		startIndex, endIndex := calculateStrictRegion(parentIndex, parentCount, childCount)
		centerPos := (startIndex + endIndex) / 2
		distance := ut.Abs(childIndex - centerPos)
		if distance < minDistance {
			minDistance = distance
			bestParent = parentIndex
		}
	}
	return bestParent
}

// 为父节点添加额外的子节点连接，允许多个父节点连接同一个子节点
func addAdditionalConnections(parent *MapNode, parentIndex int, parentLayer []*MapNode, childCount int) {
	// 计算当前父节点可以连接的子节点范围（无交叉约束）
	allowedChildren := calculateAllowedChildren(parentIndex, parentLayer, childCount)
	// 过滤掉已经连接的子节点
	availableChildren := make([]int32, 0)
	for _, childIndex := range allowedChildren {
		if !slices.Contains(parent.Children, childIndex) {
			availableChildren = append(availableChildren, childIndex)
		}
	}
	// 随机添加1-2个额外连接
	if count := int32(len(availableChildren)); count > 0 {
		maxAdditional := ut.MinInt32(2, count)
		counts := ADD_FATHER_NODE_COUNT_WEIGHT[maxAdditional-1]
		if additionalCount := counts[ut.RandomInt32(0, int32(len(counts))-1)]; additionalCount > 0 {
			// 随机打乱可用子节点
			for i := len(availableChildren) - 1; i > 0; i-- {
				j := ut.RandomInt32(0, int32(i))
				availableChildren[i], availableChildren[j] = availableChildren[j], availableChildren[i]
			}
			// 添加额外连接
			for i := range additionalCount {
				parent.Children = append(parent.Children, availableChildren[i])
			}
		}
	}
}

// 计算父节点可以连接的所有子节点（不违反无交叉原则）
func calculateAllowedChildren(parentIndex int, parentLayer []*MapNode, childCount int) []int32 {
	parentCount := len(parentLayer)

	// 扩展区域：检查与相邻父节点的连接是否会造成交叉
	minAllowed := 0
	maxAllowed := childCount - 1

	// 检查左边的父节点
	for leftParent := range parentIndex {
		if len(parentLayer[leftParent].Children) > 0 {
			// 找到左边父节点连接的最右边的子节点
			maxChildOfLeft := int32(-1)
			for _, child := range parentLayer[leftParent].Children {
				if child > maxChildOfLeft {
					maxChildOfLeft = child
				}
			}
			// 当前父节点不能连接到比这个更左的子节点
			if int(maxChildOfLeft) >= minAllowed {
				minAllowed = int(maxChildOfLeft)
			}
		}
	}

	// 检查右边的父节点
	for rightParent := parentIndex + 1; rightParent < parentCount; rightParent++ {
		if len(parentLayer[rightParent].Children) > 0 {
			// 找到右边父节点连接的最左边的子节点
			minChildOfRight := int32(childCount)
			for _, child := range parentLayer[rightParent].Children {
				if child < minChildOfRight {
					minChildOfRight = child
				}
			}
			// 当前父节点不能连接到比这个更右的子节点
			if int(minChildOfRight) <= maxAllowed {
				maxAllowed = int(minChildOfRight)
			}
		}
	}

	// 生成允许的子节点列表
	allowedChildren := make([]int32, 0)
	for i := minAllowed; i <= maxAllowed; i++ {
		allowedChildren = append(allowedChildren, int32(i))
	}

	return allowedChildren
}
