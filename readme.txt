
==================================安装go==================================
wget https://dl.google.com/go/go1.24.5.linux-amd64.tar.gz
// 解压
tar -C /usr/local -zxvf go1.24.5.linux-amd64.tar.gz
// 打开profile
vim /etc/profile
// 在最后一行添加
export GOROOT=/usr/local/go
export PATH=$PATH:$GOROOT/bin
// 保存退出后source一下
source /etc/profile
// 最后修改GOPROXY
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GO111MODULE="on"
go env -w GOBIN="/usr/local/go/bin"

==================================安装git==================================
yum -y install git-core
// 执行过后每次git命令就不会再要求输入账号密码
git config --global credential.helper store
// 拉服务器代码
git clone https://e.coding.net/twomiles/jiuwanmu/slg-server.git
// 拷贝本地文件
cp ...
// 安装依赖
go mod tidy

==================================安装nats==================================
wget https://github.com/nats-io/nats-server/releases/download/v2.9.17/nats-server-v2.9.17-linux-amd64.tar.gz
// 解压到/usr/loacl里面
mkdir /usr/local/nats && tar -xzvf nats-server-v2.9.17-linux-amd64.tar.gz -C /usr/local/nats --strip-components 1
// 创建配置文件 注意net用局域网ip
vi nats.config
---------------------
port: 4222
net: '0.0.0.0'
max_payload: 4000000
---------------------
vi start.sh
---------------------
#!/bin/bash
nohup ./nats-server -c ./nats.config -m 8222  > output.txt 2>&1 &
---------------------

==================================安装consul==================================
wget https://releases.hashicorp.com/consul/1.15.2/consul_1.15.2_linux_amd64.zip
unzip consul_1.15.2_linux_amd64.zip && mkdir /usr/local/consul && mv consul /usr/local/consul
// 创建data文件
mkdir data
// 创建启动文件
vi start.sh
---------------------
#!/bin/bash
nohup ./consul agent -dev -ui -client=0.0.0.0 -data-dir=/usr/local/consul/data/ > output.txt 2>&1 &
echo 'consul start success'
---------------------