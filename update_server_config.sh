#!/bin/bash

# 配置文件路径
config_file="./bin/conf/server.json"

# 要新增的参数值
new_sid=$1

# 构建要新增的 JSON 对象
new_game_obj=$(cat <<EOF
{
  "Id": "game$new_sid",
  "ProcessID": "game$new_sid",
  "Settings": {
    "Sid": $new_sid
  }
}
EOF
)

# 使用 jq 命令将新对象添加到配置文件的 "game" 数组中
jq ".Module.game += [$new_game_obj]" "$config_file" > temp_config.json
mv temp_config.json "$config_file"

# 修改pm2配置文件

# 创建新的元素
new_element=$(cat <<EOF
  {
    name: 'game$new_sid',
    script: 'bin/server/main',
    args: '-pid game$new_sid',
    cwd: '/projects/slg-server'
  },
EOF
)

# 在pm配置文件中添加新元素
awk -v new_element="$new_element" '/^ *apps : \[/{print;print new_element;next}1' ecosystem.config.js > temp.js && mv temp.js ecosystem.config.js

# 通过pm2启动
./pm2_run.sh game$new_sid