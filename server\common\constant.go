package g

// 服务器区域类型
const (
	SERVER_AREA_CHINA = "china" //国内
	SERVER_AREA_HK    = "hk"    //香港
)

// 服务器类型
const (
	SERVER_TYPE_LOGIN = "login" // 登录服
	SERVER_TYPE_LOBBY = "lobby" // 大厅服
	SERVER_TYPE_GAME  = "game"  // 游戏服
	SERVER_TYPE_HTTP  = "http"  // 工具服
	SERVER_TYPE_CHAT  = "chat"  // 聊天服
	SERVER_TYPE_GATE  = "gate"  // 连接服

	SERVER_TYPE_DEVOLOPMENT = "development" // 开发服
)

// 登录类型
const (
	LOGIN_TYPE_GUEST    = "guest"
	LOGIN_TYPE_WX       = "wx"
	LOGIN_TYPE_ACCOUNT  = "account"
	LOGIN_TYPE_GOOGLE   = "google"
	LOGIN_TYPE_APPLE    = "apple"
	LOGIN_TYPE_FACEBOOK = "facebook"
	LOGIN_TYPE_TWITTER  = "twitter"
	LOGIN_TYPE_LINE     = "line"
)

const (
	LOGIN_ID_TYPE_GUEST    = "guest_id"
	LOGIN_ID_TYPE_WX       = "openid"
	LOGIN_ID_TYPE_ACCOUNT  = "account"
	LOGIN_ID_TYPE_GOOGLE   = "google_openid"
	LOGIN_ID_TYPE_APPLE    = "apple_openid"
	LOGIN_ID_TYPE_FACEBOOK = "facebook_openid"
	LOGIN_ID_TYPE_TWITTER  = "twitter_openid"
	LOGIN_ID_TYPE_LINE     = "line_openid"
)

// 数据库
const (
	DB_COLLECTION_NAME_USER = "user"
)

// 用户状态
const (
	USER_STATE_OFFLINE  = iota // 离线
	USER_STATE_LOGIN           // 登陆中
	USER_STATE_ONLINE          // 在线
	USER_STATE_MALLOC          // 离线分配大厅服中
	USER_STATE_IN_CACHE        // 离线但在内存中
)

// 踢人类型
const (
	KICK_NOTIFY_TYPE_NONE  = -1 // 不通知
	KICK_NOTIFY_TYPE_OTHER = 0  // 挤号
	KICK_NOTIFY_TYPE_BAN   = 1  // 封停
	KICK_NOTIFY_TYPE_GM    = 2  // 后台踢人
)
