package fsp

// 帧数 1秒多少帧同步一次
const SYNC_RATE = 10

const MOVE_SPEED = 300

// 指令
const (
	FSPCMD_NONE      = iota
	FSPCMD_TIME_PAST       //时间流逝 1
	FSPCMD_JOIN            //玩家加入 2
	FSPCMD_LEAVE           //玩家离线 3
	FSPCMD_EXIT            //玩家退出游戏 4
	FSPCMD_MOVE            //移动 5
	FSPCMD_ATTACK          //攻击 6
	FSPCMD_ROLL            //翻滚 7
	FSPCMD_SKILL           //技能 8
	FSPCMD_BEGIN     = 100 //开始游戏
	FSPCMD_END       = 101 //游戏结束
)
