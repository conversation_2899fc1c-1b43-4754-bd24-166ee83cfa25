syntax = "proto3";

package proto;

option go_package = "./pb";

//-----------------------------数据结构------------------------------------

// 二维坐标数据
message Vec2 {
  int32 x = 1; //x轴
  int32 y = 2; //y轴
}

// int32数组
message Int32ArrayInfo { 
  repeated int32 arr = 1;
}

// 玩家数据
message UserInfo {
  string uid                              = 1; //uid
  string loginType                        = 2; //登录方式
  repeated int32 totalGameCount           = 3; //总局数
  int32 loginDayCount                     = 4; //登录天数
  string nickname                         = 5; //昵称
  int64 createTime                        = 6; //创建时间
  int64 sumOnlineTime                     = 7; //累计在线时间
  string sessionId                        = 8; //每次登录唯一id
  int32 roleId                            = 9; //当前使用的角色id
}

// 一个英雄
message Hero {
  string uid  = 1;
  int32 id    = 2;
  int32 lv    = 3; //等级
  repeated Int32ArrayInfo attrs = 4; //属性
  int32 cost      = 5; //费用
  int32 areaType  = 6; //区域类型
  int32 index     = 7; //位置
}

// 酒馆
message TavernInfo {
	int32 Id = 1; //id
  repeated Hero heros = 2; //英雄列表
}

// 遭遇信息
message EncounterInfo {
  int32 type        = 1;
  int32 updateCost  = 2; //刷新费用
  PlayerInfo data_1 = 3; //玩家信息
  TavernInfo data_3 = 4; //酒馆信息
}

// 玩家信息
message PlayerInfo {
  string uid              = 1;
  string nickname         = 2;
  map<int32, Hero> battleAreas  = 3; //战斗区域
  map<int32, Hero> prepareAreas = 4; //备战区域
  int32 roleId            = 5; //角色id
  int32 day               = 6; //天数
  int32 hour              = 7; //小时
  int32 hp                = 8; //血量
  int32 winCount          = 9; //胜利次数
  int32 gold              = 10; //金币
  int32 earnings          = 11; //收益
}

// 游戏数据
message GameData {
  PlayerInfo player       = 1; //自己的信息
  EncounterInfo encounter = 2; //遭遇信息
  repeated int32 mapPaths = 3; //地图路径
}

// 地图节点
message MapNode {
  int32 nodeId            = 1; //节点id
  repeated int32 children = 2; //可前往的节点id
}

// 一层
message MapLayer {
  repeated MapNode nodes = 1; //节点列表
}

// 地图数据
message MapData {
  repeated MapLayer maps = 1; //地图数据
}

//-----------------------------协议------------------------------------

// 服务器返回协议
message S2C_RESULT {
  bytes data    = 1;
  string error  = 2;
}

// 游客登录
message LOGIN_HD_GUESTLOGIN_C2S {
  string guestId    = 1; //游客id
  string nickname   = 2; //昵称
  string platform   = 3; //平台
}
// 游客登录返回
message LOGIN_HD_GUESTLOGIN_S2C {
  string accountToken = 1; //登录token
  string guestId      = 2; //游客id
}

// 尝试登录
message LOBBY_HD_TRYLOGIN_C2S {
  string accountToken   = 1; //登录token
  string lang           = 2; //语言
  string platform       = 3; //平台
  string version        = 4; //版本
}
// 尝试登录 返回
message LOBBY_HD_TRYLOGIN_S2C {
  UserInfo user       = 1;  //玩家信息
  string accountToken = 2;  //登录token
  int32 banAccountType        = 3; //封禁类型
  int64 banAccountSurplusTime = 4; //封禁剩余时间
  PlayerInfo gameBaseData     = 5; //游戏基础数据
}

// 进入游戏
message GAME_HD_ENTRY_C2S {
  string nickname         = 1;
	int32 roleId            = 2; //角色id
}
message GAME_HD_ENTRY_S2C {
  MapData mapData   = 1; //地图数据
  GameData gameData = 2; //游戏数据
}

// 选择地图节点
message GAME_HD_SELECTMAPNODE_C2S {
  int32 index = 1; //选择的物品索引
}
message GAME_HD_SELECTMAPNODE_S2C {
  GameData gameData = 1; //游戏数据
}

// 购买英雄
message GAME_HD_BUYHERO_C2S {
  string heroUid  = 1; //选择的英雄的uid
  int32 areaType  = 2; //区域类型
  int32 useIndex  = 3; //使用位置
}
message GAME_HD_BUYHERO_S2C {
  GameData gameData = 1; //游戏数据
}

//-----------------------------服务器主动推送------------------------------------

// 用户数据更新通知
message LOBBY_ONUPDATEUSERINFO_NOTIFY {
  repeated OnUpdatePlayerInfoNotify list = 1; //玩家数据更新通知列表
}

// 玩家数据更新通知数据
message OnUpdatePlayerInfoNotify {
  int32 type = 1; //类型
}

// 主动踢下线通知
message GAME_ONKICK_NOTIFY {
  int32 type    = 1;
  int32 banType = 2;
}