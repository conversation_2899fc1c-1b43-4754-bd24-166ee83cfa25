#!/bin/bash
pem="/usr/local/bin/jiuwanmu.pem"
CMD_MONGO_IMPORT="/usr/local/bin/mongoimport"
CMD_MONGO="/usr/local/bin/mongo"

host='127.0.0.1'
port='27017'
dbName='slgsrv_test'
uname='slg_test'
password='slg_test'
# 测试服默认启动的7区
sid="7"

file=""
YES_NO_ANSWER=""
force=""
transid=""

show_usage() {
  cat <<EOF
  说明: 参数传递格式是[key=value],带*则是必传参数。
  例如: db_copy -f=./E202301121033.tar.gz
  可传递参数列表如下：
  1、-f   db数据压缩文件地址,不传递则发起远程调用去导出并下载.
  2、-h   展示脚本使用方法.
  3、-s   导出数据的目标服务器id,如果不传递会弹出选择项,-c无法取消.
  4、-c   -c=any 无提示直接运行(非必要不会提示输入).
EOF
}

read_yes_no() {
  local answer=blah
  while [[ ${answer} != "yes" && ${answer} != "no" ]]; do
    echo "   Enter (Yes/No)"
    read answer
    answer=$(echo "${answer,,}") #To lowercase
  done

  if [[ "${answer}" == "yes" ]]; then
    answer="y"
  else
    answer="n"
  fi
  YES_NO_ANSWER=${answer}
}

exec_sql() {
  local sql="db.$1.deleteMany({"
  if [[ $2 != "" ]]; then
    sql="$sql$2:$sid"
  fi
  sql="$sql})"
  local result=$($CMD_MONGO mongodb://"$uname":"$password"@"$host":"$port"/"$dbName" --authenticationDatabase="$dbName" --eval "$sql")
  if [[ $(echo "$result" | grep -e "exception") != '' ]]; then
    printf "%-s\n" "$1 - > 执行db语句出错: $(echo "$result" | grep -e "Error")"
  fi
  local docs=${result#*"MongoDB server version"}
  docs=${docs#*"{"}
  docs="{ $docs"
  printf "%-1s %-10s %-1s %-4s %-s\n" "[" "$1" "]" "清理结果" "$docs"
}

exec_import() {
  local col=$1
  local file=$2
  local result=$($CMD_MONGO_IMPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$dbName" -d "$dbName" -c "$col" --file "$file" >/dev/null 2>&1)
}

main() {
  for i in "$@"; do
    local r=${i#*-f=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      file=$r
    fi
    r=${i#*-h=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      show_usage
    fi
    r=${i#*-c=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      force=$r
    fi
    r=${i#*-s=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      transid=$r
    fi
  done
  if [[ $force == "" ]]; then
    echo "测试服默认启动的7区,还原数据时原7区数据会被完全覆盖,是否继续?"
    read_yes_no
    if [ "${YES_NO_ANSWER}" != "y" ]; then
      exit 1
    fi
  fi

  # 杀掉正在运行的进程
  netstat -tnlp | grep -w 4653 | awk '{print $7}' | awk -F"/" '{print $1}' | xargs kill

  local time=""
  if [[ "$file" == "" ]]; then
    time=$(date "+%Y%m%d%H%M")
    local cmd="mongo_export -u=mongouser -w=jiuwanmu996 -d=slgsrv -h=172.16.32.12 -a=admin -o=E$time"
    if [[ $transid != "" ]]; then
      cmd="$cmd -s=$transid"
    fi
    # 远程调用
    ssh -i "$pem" root@114.132.228.138 "$cmd"
    # 下载文件
    echo "==> 下载压缩文件,需要等待一段时间...."
    scp -i "$pem" root@114.132.228.138:"/root/E$time.tar.gz" ./
    # 删除远端文件
    ssh -i "$pem" root@114.132.228.138 "rm -rf /root/E$time.tar.gz"
    echo "==> 远端文件已经删除."
    file="./E$time.tar.gz"
  fi
  # 解压文件
  tar -xvf "$file" >/dev/null 2>&1
  if [[ $time == "" ]]; then
    time=${file#*E}
    time=${time%.*}
    time=${time%.*}
  fi
  # 修正表数据
  local match=$(grep <"./E$time/room" -owE "\"id\":\w+")
  if [[ "$match" == "" ]]; then
    echo "==> 无法找到room表区服数据,这可能不是一个有效导出,无法继续操作."
    exit 1
  fi
  local source_sid=${match#*:}
  echo "==> 区服数据:$source_sid"
  # 清理旧区服数据
  exec_sql "user" "sid"
  exec_sql "room" "id"
  exec_sql "alliance" "sid"
  exec_sql "game" "sid"
  exec_sql "bazaar_$sid"
  exec_sql "player_$sid"
  exec_sql "world_$sid"

  echo "==> 开始数据导入:$source_sid"
  # 恢复数据
  exec_import "user" "./E$time/user"
  exec_import "room" "./E$time/room"
  exec_import "alliance" "./E$time/alliance"
  exec_import "bazaar_$sid" "./E$time/bazaar"
  exec_import "game" "./E$time/game"
  exec_import "player_$sid" "./E$time/player"
  exec_import "world_$sid" "./E$time/world"

  # 更新sid和id
  $CMD_MONGO mongodb://"$uname":"$password"@"$host":"$port"/"$dbName" --authenticationDatabase="$dbName" --eval "db.user.updateMany({sid:$source_sid},{'\$set':{sid:$sid}})" >/dev/null 2>&1
  $CMD_MONGO mongodb://"$uname":"$password"@"$host":"$port"/"$dbName" --authenticationDatabase="$dbName" --eval "db.room.updateOne({id:$source_sid},{'\$set':{id:$sid,game_over_info:null}})" >/dev/null 2>&1
  $CMD_MONGO mongodb://"$uname":"$password"@"$host":"$port"/"$dbName" --authenticationDatabase="$dbName" --eval "db.alliance.updateMany({sid:$source_sid},{'\$set':{sid:$sid}})" >/dev/null 2>&1
  $CMD_MONGO mongodb://"$uname":"$password"@"$host":"$port"/"$dbName" --authenticationDatabase="$dbName" --eval "db.game.updateMany({sid:$source_sid},{'\$set':{sid:$sid}})" >/dev/null 2>&1
  # 删除解压文件
  rm -rf "./E$time"
  echo "数据恢复完成,压缩文件保留:$file"
  # 启动游戏
  cd /projects/slg-server-test/ && sh run_release.sh
  echo "游戏启动命令执行完成,请自行检查是否正常： tail -f /projects/slg-server-test/bin/logs/development.log"
}

main "$@"
